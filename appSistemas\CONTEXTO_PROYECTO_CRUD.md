# Contexto del Proyecto - Sistema CRUD de Clientes

## Descripción General
Este proyecto es una aplicación web PHP que implementa un sistema CRUD (Create, Read, Update, Delete) completo para la gestión de clientes. La aplicación permite registrar, visualizar, editar y eliminar información de clientes en una base de datos MySQL.

## Estructura del Proyecto

### Archivos Principales
- **index.php**: Página principal con formulario de registro
- **conexion.php**: Configuración de conexión a la base de datos
- **registro_cliente.php**: Procesamiento del registro de nuevos clientes
- **lista_cliente.php**: Visualización de todos los clientes registrados
- **editar.php**: Formulario para editar información de clientes
- **editar_cliente.php**: Procesamiento de la edición de clientes
- **eliminar.php**: Eliminación de clientes
- **css/estilos.css**: Estilos CSS personalizados
- **bk_appsistemas.sql**: Backup de la base de datos

### Estructura de Directorios
```
appSistemas/
├── index.php
├── conexion.php
├── registro_cliente.php
├── lista_cliente.php
├── editar.php
├── editar_cliente.php
├── eliminar.php
├── bk_appsistemas.sql
├── css/
│   └── estilos.css
└── img/
    ├── borrar.png
    ├── canada.png
    └── cv.png
```

## Base de Datos

### Configuración de Conexión
- **Servidor**: localhost
- **Usuario**: root
- **Contraseña**: (vacía)
- **Base de datos**: prueba

### Tabla `cliente`
```sql
CREATE TABLE `cliente` (
  `id_cliente` int(11) NOT NULL AUTO_INCREMENT,
  `nombre` varchar(50) NOT NULL,
  `apellido` varchar(50) NOT NULL,
  `email` varchar(50) NOT NULL,
  `usuario` varchar(50) NOT NULL,
  `contraseña` varchar(50) NOT NULL,
  `celular` varchar(50) NOT NULL,
  `fecha_registro` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
  PRIMARY KEY (`id_cliente`)
);
```

## Funcionalidades CRUD

### 1. CREATE (Crear)
**Archivos involucrados**: `index.php`, `registro_cliente.php`

**Proceso**:
- El usuario completa el formulario en `index.php`
- Los datos se envían por POST a `registro_cliente.php`
- La contraseña se encripta usando `password_hash()`
- Se ejecuta una consulta INSERT para guardar el cliente
- Se muestra mensaje de confirmación

**Campos del formulario**:
- Nombres (nombre)
- Apellidos (apellido)
- Email (correo)
- Usuario (usuario)
- Contraseña (clave) - se encripta automáticamente
- Celular (telefono)

### 2. READ (Leer)
**Archivo involucrado**: `lista_cliente.php`

**Proceso**:
- Se ejecuta consulta SELECT para obtener todos los clientes
- Los datos se muestran en una tabla HTML con Bootstrap
- Cada fila incluye botones de acción (editar/eliminar)
- Utiliza iconos para las acciones

**Campos mostrados**:
- ID del cliente
- Nombre y apellido
- Email
- Usuario
- Contraseña (encriptada)
- Celular
- Fecha de registro
- Acciones (editar/eliminar)

### 3. UPDATE (Actualizar)
**Archivos involucrados**: `editar.php`, `editar_cliente.php`

**Proceso**:
- `editar.php` recibe el ID del cliente por GET
- Se consulta la información actual del cliente
- Se pre-llenan los campos del formulario con los datos existentes
- Al enviar, `editar_cliente.php` procesa la actualización
- Se ejecuta consulta UPDATE y redirecciona a la lista

### 4. DELETE (Eliminar)
**Archivo involucrado**: `eliminar.php`

**Proceso**:
- Se recibe el ID del cliente por GET
- Se ejecuta consulta DELETE
- Redirecciona automáticamente a la lista de clientes
- Incluye confirmación JavaScript antes de eliminar

## Tecnologías Utilizadas

### Backend
- **PHP**: Lenguaje de programación principal
- **MySQL**: Base de datos relacional
- **MySQLi**: Extensión para conexión con MySQL

### Frontend
- **HTML5**: Estructura de las páginas
- **CSS3**: Estilos personalizados
- **Bootstrap 5.3.7**: Framework CSS para diseño responsivo
- **JavaScript**: Confirmación de eliminación

### Seguridad
- **Encriptación de contraseñas**: Uso de `password_hash()` con PASSWORD_DEFAULT
- **Validación HTML**: Campos requeridos en formularios

## Patrones de Diseño y Arquitectura

### Estructura de Archivos
- **Separación de responsabilidades**: Cada archivo tiene una función específica
- **Reutilización**: `conexion.php` se incluye en todos los archivos que necesitan BD
- **Modularidad**: CSS e imágenes en carpetas separadas

### Flujo de Datos
1. **Registro**: index.php → registro_cliente.php → Base de datos
2. **Listado**: lista_cliente.php → Base de datos → Tabla HTML
3. **Edición**: lista_cliente.php → editar.php → editar_cliente.php → Base de datos
4. **Eliminación**: lista_cliente.php → eliminar.php → Base de datos

## Características de la Interfaz

### Diseño Visual
- **Colores principales**: Azul (#204862), Celeste (deepskyblue), Rojo (crimson)
- **Tipografía**: Sans-serif
- **Layout**: Responsivo con Bootstrap
- **Formularios**: Diseño centrado con bordes redondeados

### Experiencia de Usuario
- **Formularios intuitivos**: Placeholders descriptivos
- **Confirmaciones**: Alert JavaScript para eliminación
- **Navegación**: Botones con iconos para acciones
- **Feedback**: Mensajes de confirmación tras operaciones

## Consideraciones para Exámenes Similares

### Posibles Modificaciones
1. **Agregar campos**: Dirección, fecha de nacimiento, etc.
2. **Validaciones**: Email único, formato de teléfono
3. **Búsqueda**: Filtros por nombre, email, etc.
4. **Paginación**: Para listas grandes de clientes
5. **Autenticación**: Sistema de login/logout

### Mejoras de Seguridad
1. **Prepared Statements**: Prevenir inyección SQL
2. **Validación del lado del servidor**: Verificar datos antes de insertar
3. **Sanitización**: Limpiar datos de entrada
4. **HTTPS**: Conexiones seguras
5. **Tokens CSRF**: Protección contra ataques

### Funcionalidades Adicionales
1. **Exportar datos**: CSV, PDF
2. **Importar datos**: Carga masiva
3. **Historial**: Registro de cambios
4. **Roles de usuario**: Admin, usuario normal
5. **API REST**: Para integración con otras aplicaciones

## Comandos SQL Útiles

### Consultas Básicas
```sql
-- Seleccionar todos los clientes
SELECT * FROM cliente;

-- Buscar por nombre
SELECT * FROM cliente WHERE nombre LIKE '%juan%';

-- Contar registros
SELECT COUNT(*) FROM cliente;

-- Ordenar por fecha
SELECT * FROM cliente ORDER BY fecha_registro DESC;
```

### Mantenimiento
```sql
-- Limpiar tabla
TRUNCATE TABLE cliente;

-- Resetear AUTO_INCREMENT
ALTER TABLE cliente AUTO_INCREMENT = 1;

-- Backup
mysqldump -u root -p prueba > backup.sql;
```

Este contexto te proporciona toda la información necesaria para entender y trabajar con proyectos CRUD similares en tus exámenes.
