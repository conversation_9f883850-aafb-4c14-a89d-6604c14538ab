<?php 

include("conexion.php");

$nombres = $_POST['nombre'];
$apellidos = $_POST['apellido'];
$email = $_POST['correo'];
$usuario = $_POST['usuario'];
$password = $_POST['clave'];
$celular = $_POST['telefono'];

$password = password_hash($password, PASSWORD_DEFAULT);

$consulta = "INSERT INTO cliente (nombre, apellido, email, usuario, contraseña, celular) VALUES ('$nombres', '$apellidos', '$email', '$usuario', '$password', '$celular')";

$resultado = mysqli_query($conexion, $consulta);

if (!$resultado) {
	echo "CLiente no registrado";
}else{
	echo "CLIENTE SE REGISTRO CORRECTAMENTE";
}

 ?>