<?php 

include("conexion.php");
?>

<!doctype html>
<html lang="en">
  <head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <title>Bootstrap demo</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.7/dist/css/bootstrap.min.css" rel="stylesheet" integrity="sha384-LN+7fdVzj6u52u30Kp6M/trliBMCMKTyK833zpbD+pXdCLuTusPj697FH4R/5mcr" crossorigin="anonymous">
  </head>
  <body>   
    <script>
      function eliminar(){
        var respuesta = confirm("Desea eliminar al cliente");
        return respuesta
      }
    </script>
    <div class="container">
      <br>
    <center><h1>REGISTRO DEL CLIENTE</h1></center><br>

    <table class="table table-striped table-hover">
    <thead>
      <th>N</th>
      <th>Nombre</th>
      <th>A<PERSON>lido</th>
      <th>Email</th>
      <th>Usuario</th>
      <th>Contraseña</th>
      <th>Celular</th>
      <th>Fecha Registro</th>
      <th>Acciones</th>
    </thead>

      <?php
      $cliente = "SELECT * FROM cliente";
      $ejecutar = mysqli_query($conexion, $cliente);

      while ($fila = mysqli_fetch_assoc($ejecutar)) {
      ?>
      <tr>
        <td><?php echo $fila["id_cliente"]; ?></td>
        <td><?php echo $fila["nombre"]; ?></td>
        <td><?php echo $fila["apellido"]; ?></td>
        <td><?php echo $fila["email"]; ?></td>
        <td><?php echo $fila["usuario"]; ?></td>
        <td><?php echo $fila["contraseña"]; ?></td>
        <td><?php echo $fila["celular"]; ?></td>
        <td><?php echo $fila["fecha_registro"]; ?></td>
        <td>
          <a href="editar.php?id=<?= $fila['id_cliente'] ?>"><button type="button" class="btn btn-warning"><img src="img/cv.png"></button></a>    
          <a onclick="return eliminar()" href="eliminar.php?id=<?= $fila['id_cliente'] ?>"><button type="button" class="btn btn-danger"><img src="img/borrar.png"></button></a>
        </td>
      </tr>
      <?php
      }
      ?>
   
</table>
</div>



    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.7/dist/js/bootstrap.bundle.min.js" integrity="sha384-ndDqU0Gzau9qJ1lfW4pNLlhNTkCfHzAVBReH9diLvGRem5+R9g2FzA8ZGN954O5Q" crossorigin="anonymous"></script>
  </body>
</html>